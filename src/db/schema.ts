import {
  pgTable,
  uuid,
  varchar,
  text,
  boolean,
  timestamp,
  date,
  integer,
  json,
  pgEnum,
  index,
  uniqueIndex
} from 'drizzle-orm/pg-core';
import { relations } from 'drizzle-orm';

// ==== 枚举定义 ====
export const fieldTypeEnum = pgEnum('field_type', [
  'text', 'date', 'number', 'boolean', 'select', 'json'
]);

export const searchTypeEnum = pgEnum('search_type', [
  'exact', 'contains', 'range', 'date_range', 'starts_with', 'ends_with'
]);

export const filterTypeEnum = pgEnum('filter_type', [
  'select', 'input', 'date_range', 'checkbox', 'multi_select', 'range'
]);

export const statisticsTypeEnum = pgEnum('statistics_type', [
  'count', 'sum', 'avg', 'min_max', 'group_by'
]);

// ==== 用户管理相关模型 ====
export const users = pgTable('User', {
  id: uuid('id').primaryKey().defaultRandom(),
  email: varchar('email', { length: 255 }).unique().notNull(),
  password: text('password').notNull(),
  name: varchar('name', { length: 100 }).notNull(),
  membershipType: varchar('membershipType', { length: 20 }).default('free').notNull(),
  membershipExpiry: timestamp('membershipExpiry'),
  isActive: boolean('isActive').default(true).notNull(),
  emailVerified: boolean('emailVerified').default(false).notNull(),
  createdAt: timestamp('createdAt').defaultNow().notNull(),
  updatedAt: timestamp('updatedAt').defaultNow().notNull(),
});

export const userSessions = pgTable('UserSession', {
  id: uuid('id').primaryKey().defaultRandom(),
  userId: uuid('userId').notNull().references(() => users.id, { onDelete: 'cascade' }),
  token: varchar('token', { length: 255 }).unique().notNull(),
  expiresAt: timestamp('expiresAt').notNull(),
  createdAt: timestamp('createdAt').defaultNow().notNull(),
});

// ==== 配置管理模型 ====
export const databaseConfigs = pgTable('DatabaseConfig', {
  id: uuid('id').primaryKey().defaultRandom(),
  code: varchar('code', { length: 50 }).unique().notNull(),
  name: varchar('name', { length: 100 }).notNull(),
  category: varchar('category', { length: 50 }).notNull(),
  description: text('description'),
  accessLevel: varchar('accessLevel', { length: 20 }).default('free').notNull(),

  // 数据库映射配置
  tableName: varchar('tableName', { length: 100 }),
  schemaName: varchar('schemaName', { length: 100 }).default('public'),

  // 性能优化配置
  connectionPool: varchar('connectionPool', { length: 50 }).default('default'),
  queryTimeout: integer('queryTimeout').default(30000), // 查询超时(毫秒)

  // 缓存策略配置
  cacheStrategy: varchar('cacheStrategy', { length: 50 }).default('redis'),
  cacheTTL: integer('cacheTTL').default(300), // 缓存TTL(秒)

  // 导出配置
  maxExportLimit: integer('maxExportLimit').default(10000).notNull(),
  defaultExportLimit: integer('defaultExportLimit').default(1000).notNull(),
  exportFormats: json('exportFormats').default(['csv', 'xlsx']),

  // 状态和排序
  isActive: boolean('isActive').default(true).notNull(),
  sortOrder: integer('sortOrder').notNull(),

  // 时间戳
  createdAt: timestamp('createdAt').defaultNow().notNull(),
  updatedAt: timestamp('updatedAt').defaultNow().notNull(),

  // JSON 配置字段
  defaultSort: json('defaultSort'),
  exportConfig: json('exportConfig'),
  indexStrategy: json('indexStrategy'), // 索引优化策略
});

export const fieldConfigs = pgTable('FieldConfig', {
  id: uuid('id').primaryKey().defaultRandom(),
  databaseCode: varchar('databaseCode', { length: 50 }).notNull(),
  fieldName: varchar('fieldName', { length: 100 }).notNull(),
  displayName: varchar('displayName', { length: 100 }).notNull(),
  fieldType: fieldTypeEnum('fieldType').default('text').notNull(),
  isVisible: boolean('isVisible').default(true).notNull(),
  isSearchable: boolean('isSearchable').default(false).notNull(),
  isFilterable: boolean('isFilterable').default(false).notNull(),
  isAdvancedSearchable: boolean('isAdvancedSearchable').default(false).notNull(),
  isSortable: boolean('isSortable').default(false).notNull(),
  sortOrder: integer('sortOrder').default(0).notNull(),
  listOrder: integer('listOrder').default(0).notNull(),
  detailOrder: integer('detailOrder').default(0).notNull(),
  searchType: searchTypeEnum('searchType').default('contains').notNull(),
  filterType: filterTypeEnum('filterType').default('select').notNull(),
  todetail: boolean('todetail').default(false).notNull(),
  isStatisticsEnabled: boolean('isStatisticsEnabled').default(false).notNull(),
  statisticsOrder: integer('statisticsOrder').default(0).notNull(),
  statisticsType: statisticsTypeEnum('statisticsType').default('count').notNull(),
  statisticsDisplayName: varchar('statisticsDisplayName', { length: 100 }),
  statisticsSortOrder: varchar('statisticsSortOrder', { length: 10 }).default('desc'),
  statisticsDefaultLimit: integer('statisticsDefaultLimit').default(5).notNull(),
  statisticsMaxLimit: integer('statisticsMaxLimit').default(50).notNull(),
  isExportable: boolean('isExportable').default(true).notNull(),
  exportOrder: integer('exportOrder').default(0).notNull(),
  exportDisplayName: varchar('exportDisplayName', { length: 100 }),
  filterOrder: integer('filterOrder').default(0).notNull(),
  isActive: boolean('isActive').default(true).notNull(),
  createdAt: timestamp('createdAt').defaultNow().notNull(),
  updatedAt: timestamp('updatedAt').defaultNow().notNull(),
  // JSON 字段暂时保留
  validationRules: json('validationRules'),
  options: json('options'),
  statisticsConfig: json('statisticsConfig'),
}, (table) => ({
  databaseCodeFieldNameIdx: uniqueIndex('fieldconfig_database_field_unique').on(table.databaseCode, table.fieldName),
  databaseCodeIdx: index('fieldconfig_database_idx').on(table.databaseCode),
  isActiveIdx: index('fieldconfig_active_idx').on(table.isActive),
  isStatisticsEnabledIdx: index('fieldconfig_statistics_idx').on(table.isStatisticsEnabled),
  filterOrderIdx: index('fieldconfig_filter_order_idx').on(table.filterOrder),
}));

// ==== 公司数据模型 ====
export const companies = pgTable('Company', {
  id: uuid('id').primaryKey().defaultRandom(),
  companyName: text('companyName').notNull(),
  companyCode: varchar('companyCode', { length: 50 }),
  companyShortName: text('companyShortName'),
  region: varchar('region', { length: 50 }).notNull(),
  province: text('province'),
  city: text('city'),
  address: text('address'),
  phone: text('phone'),
  email: text('email'),
  website: text('website'),
  establishDate: timestamp('establishDate'),
  registeredCapital: text('registeredCapital'),
  legalRepresentative: text('legalRepresentative'),
  businessScope: text('businessScope'),
  companyType: text('companyType'),
  industryCategory: text('industryCategory'),
  isListed: boolean('isListed').default(false),
  stockCode: text('stockCode'),
  notes: text('notes'),
  database: varchar('database', { length: 50 }).notNull(),
  businessKey: varchar('businessKey', { length: 200 }).unique().notNull(),
  businessKeyHash: varchar('businessKeyHash', { length: 255 }).unique(),
  dataVersion: integer('dataVersion').default(1).notNull(),
  isActive: boolean('isActive').default(true).notNull(),
  importedAt: timestamp('importedAt').defaultNow().notNull(),
  updatedAt: timestamp('updatedAt').defaultNow().notNull(),
  createdAt: timestamp('createdAt').defaultNow().notNull(),
}, (table) => ({
  databaseIdx: index('company_database_idx').on(table.database),
  businessKeyIdx: index('company_business_key_idx').on(table.businessKey),
  businessKeyHashIdx: index('company_business_key_hash_idx').on(table.businessKeyHash),
}));

// ==== 分析和日志模型 ====
export const activityLogs = pgTable('ActivityLog', {
  id: uuid('id').primaryKey().defaultRandom(),
  userId: uuid('userId').references(() => users.id),
  ip: varchar('ip', { length: 50 }).notNull(),
  userAgent: varchar('userAgent', { length: 500 }),
  path: varchar('path', { length: 500 }).notNull(),
  method: varchar('method', { length: 20 }).notNull(),
  queryParams: text('queryParams'),
  referer: varchar('referer', { length: 500 }),
  database: varchar('database', { length: 50 }),
  eventType: varchar('eventType', { length: 50 }),
  sessionId: varchar('sessionId', { length: 100 }),
  createdAt: timestamp('createdAt').defaultNow().notNull(),
}, (table) => ({
  createdAtIdx: index('activity_log_created_at_idx').on(table.createdAt),
  eventTypeIdx: index('activity_log_event_type_idx').on(table.eventType),
  databaseIdx: index('activity_log_database_idx').on(table.database),
  sessionIdIdx: index('activity_log_session_id_idx').on(table.sessionId),
  ipIdx: index('activity_log_ip_idx').on(table.ip),
  userIdIdx: index('activity_log_user_id_idx').on(table.userId),
  createdAtEventTypeIdx: index('activity_log_created_at_event_type_idx').on(table.createdAt, table.eventType),
  createdAtDatabaseIdx: index('activity_log_created_at_database_idx').on(table.createdAt, table.database),
  sessionIdEventTypeIdx: index('activity_log_session_id_event_type_idx').on(table.sessionId, table.eventType),
}));

export const searchAnalytics = pgTable('SearchAnalytics', {
  id: uuid('id').primaryKey().defaultRandom(),
  userId: uuid('userId').references(() => users.id),
  sessionId: varchar('sessionId', { length: 100 }),
  database: varchar('database', { length: 50 }).notNull(),
  searchType: varchar('searchType', { length: 20 }).notNull(),
  searchQuery: text('searchQuery'),
  sortBy: varchar('sortBy', { length: 50 }),
  sortOrder: varchar('sortOrder', { length: 10 }),
  resultsCount: integer('resultsCount'),
  searchTime: integer('searchTime'),
  ip: varchar('ip', { length: 50 }).notNull(),
  userAgent: varchar('userAgent', { length: 500 }),
  createdAt: timestamp('createdAt').defaultNow().notNull(),
  // JSON 字段
  searchFields: json('searchFields'),
  filters: json('filters'),
}, (table) => ({
  createdAtIdx: index('search_analytics_created_at_idx').on(table.createdAt),
  databaseIdx: index('search_analytics_database_idx').on(table.database),
  searchTypeIdx: index('search_analytics_search_type_idx').on(table.searchType),
  userIdIdx: index('search_analytics_user_id_idx').on(table.userId),
  sessionIdIdx: index('search_analytics_session_id_idx').on(table.sessionId),
  databaseCreatedAtIdx: index('search_analytics_database_created_at_idx').on(table.database, table.createdAt),
  searchTypeDatabaseIdx: index('search_analytics_search_type_database_idx').on(table.searchType, table.database),
}));

export const blockedIps = pgTable('BlockedIp', {
  id: uuid('id').primaryKey().defaultRandom(),
  ip: varchar('ip', { length: 50 }).unique().notNull(),
  reason: text('reason'),
  expiresAt: timestamp('expiresAt').notNull(),
  createdAt: timestamp('createdAt').defaultNow().notNull(),
});

export const dataChangeLogs = pgTable('DataChangeLog', {
  id: uuid('id').primaryKey().defaultRandom(),
  businessKey: varchar('businessKey', { length: 200 }).notNull(),
  businessKeyHash: varchar('businessKeyHash', { length: 255 }),
  operation: varchar('operation', { length: 20 }).notNull(),
  changeReason: varchar('changeReason', { length: 500 }),
  importedBy: varchar('importedBy', { length: 100 }),
  importedFrom: varchar('importedFrom', { length: 200 }),
  createdAt: timestamp('createdAt').defaultNow().notNull(),
  // JSON 字段
  oldData: json('oldData'),
  newData: json('newData'),
}, (table) => ({
  businessKeyIdx: index('data_change_log_business_key_idx').on(table.businessKey),
  businessKeyHashIdx: index('data_change_log_business_key_hash_idx').on(table.businessKeyHash),
  operationIdx: index('data_change_log_operation_idx').on(table.operation),
  createdAtIdx: index('data_change_log_created_at_idx').on(table.createdAt),
}));

export const contactSubmissions = pgTable('ContactSubmission', {
  id: uuid('id').primaryKey().defaultRandom(),
  name: varchar('name', { length: 100 }).notNull(),
  email: varchar('email', { length: 255 }).notNull(),
  subject: varchar('subject', { length: 200 }).notNull(),
  category: varchar('category', { length: 50 }).notNull(),
  message: text('message').notNull(),
  ip: varchar('ip', { length: 45 }),
  userAgent: varchar('userAgent', { length: 500 }),
  status: varchar('status', { length: 20 }).default('pending').notNull(),
  adminNotes: text('adminNotes'),
  createdAt: timestamp('createdAt').defaultNow().notNull(),
  updatedAt: timestamp('updatedAt').defaultNow().notNull(),
}, (table) => ({
  emailIdx: index('contact_submission_email_idx').on(table.email),
  categoryIdx: index('contact_submission_category_idx').on(table.category),
  statusIdx: index('contact_submission_status_idx').on(table.status),
  createdAtIdx: index('contact_submission_created_at_idx').on(table.createdAt),
}));

// ==== 医疗器械数据模型 ====
export const usPremarketNotifications = pgTable('us_pmn', {
  id: uuid('id').primaryKey().defaultRandom(),
  knumber: varchar('knumber', { length: 20 }),
  applicant: text('applicant'),
  contact: text('contact'),
  street1: text('street1'),
  street2: text('street2'),
  city: varchar('city', { length: 100 }),
  state: varchar('state', { length: 50 }),
  country_code: varchar('country_code', { length: 10 }),
  zip: varchar('zip', { length: 20 }),
  postal_code: varchar('postal_code', { length: 20 }),
  datereceived: date('datereceived'),
  decisiondate: date('decisiondate'),
  decision: varchar('decision', { length: 100 }),
  reviewadvisecomm: text('reviewadvisecomm'),
  productcode: varchar('productcode', { length: 20 }),
  stateorsumm: text('stateorsumm'),
  classadvisecomm: text('classadvisecomm'),
  sspindicator: varchar('sspindicator', { length: 10 }),
  type: varchar('type', { length: 50 }),
  thirdparty: varchar('thirdparty', { length: 10 }),
  expeditedreview: varchar('expeditedreview', { length: 10 }),
  devicename: text('devicename'),
  source_file: text('source_file'),
  source_time: text('source_time'),
  decision_year: text('decision_year'),
}, (table) => ({
  knumberIdx: index('us_pmn_knumber_idx').on(table.knumber),
  applicantIdx: index('us_pmn_applicant_idx').on(table.applicant),
  decisiondateIdx: index('us_pmn_decisiondate_idx').on(table.decisiondate),
  decisionIdx: index('us_pmn_decision_idx').on(table.decision),
  productcodeIdx: index('us_pmn_productcode_idx').on(table.productcode),
  devicenameIdx: index('us_pmn_devicename_idx').on(table.devicename),
  decisionYearIdx: index('us_pmn_decision_year_idx').on(table.decision_year),
}));

export const usClasses = pgTable('us_class', {
  id: uuid('id').primaryKey().defaultRandom(),
  review_panel: text('review_panel'),
  medicalspecialty: text('medicalspecialty'),
  productcode: text('productcode'),
  devicename: text('devicename'),
  deviceclass: text('deviceclass'),
  unclassified_reason: text('unclassified_reason'),
  gmpexemptflag: text('gmpexemptflag'),
  thirdpartyflag: text('thirdpartyflag'),
  reviewcode: text('reviewcode'),
  regulationnumber: text('regulationnumber'),
  submission_type_id: text('submission_type_id'),
  definition: text('definition'),
  physicalstate: text('physicalstate'),
  technicalmethod: text('technicalmethod'),
  targetarea: text('targetarea'),
  implant_flag: text('implant_flag'),
  life_sustain_support_flag: text('life_sustain_support_flag'),
  summarymalfunctionreporting: text('summarymalfunctionreporting'),
  source_file: text('source_file'),
  source_time: text('source_time'),
}, (table) => ({
  productcodeIdx: index('us_class_productcode_idx').on(table.productcode),
  devicenameIdx: index('us_class_devicename_idx').on(table.devicename),
  deviceclassIdx: index('us_class_deviceclass_idx').on(table.deviceclass),
  medicalspecialtyIdx: index('us_class_medicalspecialty_idx').on(table.medicalspecialty),
  regulationnumberIdx: index('us_class_regulationnumber_idx').on(table.regulationnumber),
}));

// ==== 关系定义 ====
export const usersRelations = relations(users, ({ many }) => ({
  sessions: many(userSessions),
  activityLogs: many(activityLogs),
  searchAnalytics: many(searchAnalytics),
}));

export const userSessionsRelations = relations(userSessions, ({ one }) => ({
  user: one(users, {
    fields: [userSessions.userId],
    references: [users.id],
  }),
}));

export const activityLogsRelations = relations(activityLogs, ({ one }) => ({
  user: one(users, {
    fields: [activityLogs.userId],
    references: [users.id],
  }),
}));

export const searchAnalyticsRelations = relations(searchAnalytics, ({ one }) => ({
  user: one(users, {
    fields: [searchAnalytics.userId],
    references: [users.id],
  }),
}));
