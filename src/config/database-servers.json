{"servers": [{"id": "default", "name": "Default PostgreSQL Server", "host": "localhost", "port": 5432, "database": "public", "username": "postgres", "password": "postgres123", "ssl": false, "maxConnections": 20, "idleTimeout": 20, "connectTimeout": 10, "isActive": true, "description": "Default database server for all tables"}, {"id": "us_server", "name": "US Medical Device Server", "host": "*************", "port": 5432, "database": "us_medical_devices", "username": "us_user", "password": "us_password", "ssl": true, "maxConnections": 15, "idleTimeout": 30, "connectTimeout": 15, "isActive": false, "description": "Dedicated server for US medical device data"}, {"id": "eu_server", "name": "EU Medical Device Server", "host": "*************", "port": 5432, "database": "eu_medical_devices", "username": "eu_user", "password": "eu_password", "ssl": true, "maxConnections": 15, "idleTimeout": 30, "connectTimeout": 15, "isActive": false, "description": "Dedicated server for EU medical device data"}], "mappings": [{"databaseCode": "us_pmn", "serverId": "default", "tableName": "us_pmn", "isActive": true}, {"databaseCode": "us_class", "serverId": "default", "tableName": "us_class", "isActive": true}]}