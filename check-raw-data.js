#!/usr/bin/env node

/**
 * 通过 API 检查原始数据，看看是否是 API 处理过程中的问题
 */

// 使用 Node.js 18+ 内置的 fetch

async function checkRawData() {
  try {
    console.log('🔍 检查 us_pmn 数据库中的原始日期数据...\n');

    // 1. 先检查 API 返回的原始数据
    console.log('1. 检查 API 返回的原始数据:');
    const response = await fetch('http://localhost:3000/api/data/us_pmn?page=1&limit=3');
    const result = await response.json();
    
    if (result.success && result.data.length > 0) {
      console.log('✅ API 调用成功');
      console.log(`📊 返回 ${result.data.length} 条记录\n`);
      
      result.data.forEach((record, index) => {
        console.log(`记录 ${index + 1}:`);
        console.log(`  ID: ${record.id}`);
        console.log(`  K Number: ${record.knumber}`);
        console.log(`  Device Name: ${record.devicename}`);
        console.log(`  Date Received: ${record.datereceived} (类型: ${typeof record.datereceived})`);
        console.log(`  Decision Date: ${record.decisiondate} (类型: ${typeof record.decisiondate})`);
        console.log('  ---');
      });
      
      // 2. 检查字段配置
      console.log('\n2. 检查字段配置:');
      const dateFields = result.config.fields.filter(field => 
        field.fieldName === 'datereceived' || field.fieldName === 'decisiondate'
      );
      
      dateFields.forEach(field => {
        console.log(`  ${field.fieldName}:`);
        console.log(`    显示名称: ${field.displayName}`);
        console.log(`    字段类型: ${field.fieldType}`);
        console.log(`    是否可见: ${field.isVisible}`);
        console.log(`    列表顺序: ${field.listOrder}`);
      });
      
    } else {
      console.log('❌ API 调用失败或无数据');
      console.log('错误:', result.error || '未知错误');
    }
    
    // 3. 检查是否有任何记录包含日期数据
    console.log('\n3. 搜索包含日期数据的记录:');
    
    // 尝试获取更多数据来查找有日期的记录
    for (let page = 1; page <= 5; page++) {
      const pageResponse = await fetch(`http://localhost:3000/api/data/us_pmn?page=${page}&limit=20`);
      const pageResult = await pageResponse.json();
      
      if (pageResult.success && pageResult.data.length > 0) {
        const recordsWithDates = pageResult.data.filter(record => 
          record.datereceived !== null || record.decisiondate !== null
        );
        
        if (recordsWithDates.length > 0) {
          console.log(`✅ 在第 ${page} 页找到 ${recordsWithDates.length} 条包含日期的记录:`);
          recordsWithDates.slice(0, 3).forEach(record => {
            console.log(`  K Number: ${record.knumber}`);
            console.log(`  Date Received: ${record.datereceived}`);
            console.log(`  Decision Date: ${record.decisiondate}`);
            console.log('  ---');
          });
          break;
        } else {
          console.log(`  第 ${page} 页: 无日期数据`);
        }
      }
    }
    
  } catch (error) {
    console.error('❌ 检查过程中出错:', error.message);
  }
}

checkRawData().catch(console.error);
