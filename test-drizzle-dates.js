#!/usr/bin/env tsx

/**
 * 直接使用 Drizzle 测试日期字段查询
 */

import * as dotenv from 'dotenv';
dotenv.config();

import { db } from './src/lib/db-server';
import { usPremarketNotifications } from './src/db/schema';

async function testDrizzleDates() {
  try {
    console.log('🔍 使用 Drizzle 直接查询日期字段...\n');

    // 1. 查询原始数据
    console.log('1. 查询原始数据:');
    const rawData = await db
      .select({
        id: usPremarketNotifications.id,
        knumber: usPremarketNotifications.knumber,
        devicename: usPremarketNotifications.devicename,
        datereceived: usPremarketNotifications.datereceived,
        decisiondate: usPremarketNotifications.decisiondate,
      })
      .from(usPremarketNotifications)
      .limit(3);

    console.log(`✅ 查询到 ${rawData.length} 条记录\n`);
    
    rawData.forEach((record, index) => {
      console.log(`记录 ${index + 1}:`);
      console.log(`  ID: ${record.id}`);
      console.log(`  K Number: ${record.knumber}`);
      console.log(`  Device Name: ${record.devicename}`);
      console.log(`  Date Received: ${record.datereceived} (类型: ${typeof record.datereceived})`);
      console.log(`  Decision Date: ${record.decisiondate} (类型: ${typeof record.decisiondate})`);
      
      if (record.datereceived) {
        console.log(`  Date Received 详细: ${record.datereceived.toISOString()}`);
      }
      if (record.decisiondate) {
        console.log(`  Decision Date 详细: ${record.decisiondate.toISOString()}`);
      }
      console.log('  ---');
    });

    // 2. 查询特定的有日期数据的记录
    console.log('\n2. 查询特定记录 (K250972):');
    const specificRecord = await db
      .select({
        id: usPremarketNotifications.id,
        knumber: usPremarketNotifications.knumber,
        devicename: usPremarketNotifications.devicename,
        datereceived: usPremarketNotifications.datereceived,
        decisiondate: usPremarketNotifications.decisiondate,
      })
      .from(usPremarketNotifications)
      .where(eq(usPremarketNotifications.knumber, 'K250972'))
      .limit(1);

    if (specificRecord.length > 0) {
      const record = specificRecord[0];
      console.log(`  ID: ${record.id}`);
      console.log(`  K Number: ${record.knumber}`);
      console.log(`  Device Name: ${record.devicename}`);
      console.log(`  Date Received: ${record.datereceived} (类型: ${typeof record.datereceived})`);
      console.log(`  Decision Date: ${record.decisiondate} (类型: ${typeof record.decisiondate})`);
    } else {
      console.log('  未找到记录');
    }

    // 3. 查询所有字段
    console.log('\n3. 查询所有字段 (第一条记录):');
    const allFieldsData = await db
      .select()
      .from(usPremarketNotifications)
      .limit(1);

    if (allFieldsData.length > 0) {
      const record = allFieldsData[0];
      console.log('所有字段:');
      Object.keys(record).forEach(key => {
        const value = record[key];
        console.log(`  ${key}: ${value} (类型: ${typeof value})`);
      });
    }

  } catch (error) {
    console.error('❌ 测试过程中出错:', error.message);
    console.error('错误详情:', error);
  }
}

// 需要导入 eq 函数
import { eq } from 'drizzle-orm';

testDrizzleDates().catch(console.error);
