#!/usr/bin/env tsx

/**
 * 调试 API 字段过滤逻辑
 */

import * as dotenv from 'dotenv';
dotenv.config();

import { db } from './src/lib/db-server';
import { getDynamicTable } from './src/lib/drizzleTableMapping';
import { getDatabaseConfig } from './src/lib/configCache';

async function debugApiFiltering() {
  try {
    console.log('🔍 调试 API 字段过滤逻辑...\n');

    // 1. 获取配置
    const config = await getDatabaseConfig('us_pmn');
    const visibleFields = config.fields.filter(f => f.isVisible);
    
    console.log('✅ 获取配置成功');
    console.log(`📊 可见字段数量: ${visibleFields.length}`);
    
    const dateFields = visibleFields.filter(f => f.fieldType === 'date');
    console.log(`📅 日期字段数量: ${dateFields.length}`);
    dateFields.forEach(field => {
      console.log(`  - ${field.fieldName} (${field.displayName})`);
    });

    // 2. 获取原始数据
    const table = getDynamicTable('us_pmn');
    console.log('\n✅ 获取表映射成功');
    
    const rawData = await db.select().from(table).limit(1);
    console.log(`📊 原始数据条数: ${rawData.length}`);
    
    if (rawData.length > 0) {
      const item = rawData[0];
      console.log('\n🔍 原始数据分析:');
      console.log(`  ID: ${item.id}`);
      console.log(`  K Number: ${item.knumber}`);
      console.log(`  Device Name: ${item.devicename}`);
      
      // 检查日期字段
      console.log('\n📅 日期字段检查:');
      console.log(`  datereceived 存在: ${item.hasOwnProperty('datereceived')}`);
      console.log(`  datereceived 值: ${item.datereceived} (类型: ${typeof item.datereceived})`);
      console.log(`  decisiondate 存在: ${item.hasOwnProperty('decisiondate')}`);
      console.log(`  decisiondate 值: ${item.decisiondate} (类型: ${typeof item.decisiondate})`);
      
      // 检查所有属性
      console.log('\n🔍 所有属性:');
      Object.keys(item).forEach(key => {
        console.log(`  ${key}: ${item[key]} (类型: ${typeof item[key]})`);
      });
      
      // 模拟 API 过滤逻辑
      console.log('\n🔄 模拟 API 过滤逻辑:');
      const filtered = { id: item.id };
      visibleFields.forEach(fieldConfig => {
        console.log(`  检查字段 ${fieldConfig.fieldName}:`);
        console.log(`    hasOwnProperty: ${item.hasOwnProperty(fieldConfig.fieldName)}`);
        console.log(`    值: ${item[fieldConfig.fieldName]}`);
        
        if (item.hasOwnProperty(fieldConfig.fieldName)) {
          filtered[fieldConfig.fieldName] = item[fieldConfig.fieldName];
          console.log(`    ✅ 已添加到过滤结果`);
        } else {
          console.log(`    ❌ 未添加到过滤结果`);
        }
      });
      
      console.log('\n📋 过滤后的结果:');
      console.log(JSON.stringify(filtered, null, 2));
    }
    
  } catch (error) {
    console.error('❌ 调试过程中出错:', error.message);
    console.error('错误详情:', error);
  }
}

debugApiFiltering().catch(console.error);
