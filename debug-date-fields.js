#!/usr/bin/env tsx

/**
 * 直接查询数据库检查日期字段的实际数据
 */

import * as dotenv from 'dotenv';
dotenv.config();

import { db } from './src/lib/db-server';
import { sql } from 'drizzle-orm';

async function checkDateFields() {
  try {
    console.log('✅ 开始检查数据库日期字段...');

    // 使用 Drizzle 查询数据
    const { getDynamicTable } = await import('./src/lib/drizzleTableMapping');
    const usPmn = getDynamicTable('us_pmn');

    console.log('✅ 导入表映射成功');

    // 查询有日期数据的记录
    const recordsWithDates = await db
      .select({
        id: usPmn.id,
        datereceived: usPmn.datereceived,
        decisiondate: usPmn.decisiondate,
        knumber: usPmn.knumber,
        devicename: usPmn.devicename
      })
      .from(usPmn)
      .where(sql`${usPmn.datereceived} IS NOT NULL OR ${usPmn.decisiondate} IS NOT NULL`)
      .limit(5);

    console.log('\n📊 有日期数据的记录:');
    if (recordsWithDates.length === 0) {
      console.log('  ❌ 没有找到包含日期数据的记录');

      // 查询统计信息
      const stats = await db
        .select({
          total: sql<number>`COUNT(*)`,
          datereceived_not_null: sql<number>`COUNT(${usPmn.datereceived})`,
          decisiondate_not_null: sql<number>`COUNT(${usPmn.decisiondate})`
        })
        .from(usPmn);

      console.log('\n📈 日期字段统计:');
      const stat = stats[0];
      console.log(`  总记录数: ${stat.total}`);
      console.log(`  datereceived 非空: ${stat.datereceived_not_null}`);
      console.log(`  decisiondate 非空: ${stat.decisiondate_not_null}`);

    } else {
      recordsWithDates.forEach(row => {
        console.log(`  ID: ${row.id}`);
        console.log(`  K Number: ${row.knumber}`);
        console.log(`  Device: ${row.devicename}`);
        console.log(`  Date Received: ${row.datereceived}`);
        console.log(`  Decision Date: ${row.decisiondate}`);
        console.log('  ---');
      });
    }

  } catch (error) {
    console.error('❌ 数据库查询错误:', error.message);
    console.error('错误详情:', error);
  }
}

checkDateFields().catch(console.error);
